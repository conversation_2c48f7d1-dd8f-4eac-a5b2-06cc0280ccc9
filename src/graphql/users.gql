query Users($input: UsersInput!) {
  users(usersInput: $input) {
    id
    fullname
    phone
    userStatus
    role
  }
}

mutation CreateUser($input: CreateUserInput!) {
  signUp(input: $input) {
    access_token
  }
}

query User($userId: String!) {
  user(id: $userId) {
    id
    fullname
    phone
    userStatus
    role
  }
}

mutation UpdateUser($input: UpdateUserInput!) {
  updateUser(updateUserInput: $input) {
    id
  }
}

query Me {
  me {
    _id
    id
    fullname
    phone
    userStatus
    role
  }
}

mutation IndexFace($indexFaceInput: IndexFaceInput!) {
  indexFace(indexFaceInput: $indexFaceInput) {
    id
    fullname
    userStatus
    role
  }
}

mutation ClearFace($input: ClearFaceInput!) {
    clearFace(clearFaceInput: $input)
}