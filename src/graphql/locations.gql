query Locations {
  locations {
    id
    name
    description
    address
    emergencyContact
  }
}

mutation CreateLocation($createLocationInput: CreateLocationInput!) {
  createLocation(createLocationInput: $createLocationInput) {
    id
  }
}

query Location($locationId: String!) {
    location(id: $locationId) {
        _id
        id
        createdAt
        updatedAt
        name
        description
        address
        emergencyContact
    }
}

mutation UpdateLocation($id: ID!, $updateLocationInput: UpdateLocationInput!) {
    updateLocation(id: $id, updateLocationInput: $updateLocationInput) {
        id
    }
}

