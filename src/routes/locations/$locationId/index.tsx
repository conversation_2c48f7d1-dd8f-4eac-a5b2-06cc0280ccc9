import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import Table from '@/components/ui/table/index';
import { useLocationQuery, useCheckpointsQuery, useRemoveCheckpointMutation } from '@/generated/graphql';
import { createFileRoute } from '@tanstack/react-router';
import { useState } from 'react';
import CreateCheckpointForm from '@/forms/locations/CreateCheckpointForm';
import UpdateLocationForm from '@/forms/locations/UpdateLocationForm';
import { Printer, Trash2 } from 'lucide-react';
import { printCheckpointQRCode } from '@/lib/utils';
import { toast } from 'sonner';

export const Route = createFileRoute('/locations/$locationId/')({
  component: RouteComponent
});

function RouteComponent() {
  const { locationId } = Route.useParams();
  const [showCreateCheckpointModal, setShowCreateCheckpointModal] = useState(false);
  
  const { data: location } = useLocationQuery({ 
    locationId
  });

  const { data: checkpoints, refetch } = useCheckpointsQuery({
    filter: { location: locationId  }
  });

  const { mutateAsync } = useRemoveCheckpointMutation();

  const checkpointRows = checkpoints?.checkpoints.map(checkpoint => {
    return [
      checkpoint.name,
      <div className="flex gap-2" key={checkpoint.id}>
        <Button 
          variant="ghost" 
          size="icon"
          onClick={() => printCheckpointQRCode({
            id: checkpoint.id,
            locationId: locationId
          })}
        >
          <Printer className="h-4 w-4" />
        </Button>
        <Button 
          variant="ghost" 
          size="icon"
          className="text-destructive hover:text-destructive"
          onClick={() => {
            toast.promise(
              mutateAsync({ checkpointId: checkpoint.id }).then(() => {
                refetch();  // Refetch checkpoints after successful deletion
              }),
              {
                loading: 'Deleting checkpoint....',
                success: 'Checkpoint deleted successfully',
                error: 'Failed to delete checkpoint'
              }
            );
          }}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    ];
  });

  return (
    <div className="space-y-8">
      {/* Location Edit Form */}
      <div className="rounded-lg border p-4">
        <h2 className="text-lg font-semibold mb-4">Location Details</h2>
        <UpdateLocationForm 
          location={{
            id: location?.location?.id ?? '',
            name: location?.location?.name,
            description: location?.location?.description ?? undefined,
            address: location?.location?.address ?? undefined,
            emergencyContact: location?.location?.emergencyContact ?? undefined
          }} 
        />
      </div>

      {/* Checkpoints */}
      <div className="rounded-lg border p-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Checkpoints</h2>
          <Dialog open={showCreateCheckpointModal} onOpenChange={setShowCreateCheckpointModal}>
            <DialogTrigger asChild>
              <Button>Add Checkpoint</Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create Checkpoint</DialogTitle>
              </DialogHeader>
              <Separator />
              <CreateCheckpointForm 
                locationId={locationId} 
                onSuccess={() => setShowCreateCheckpointModal(false)} 
              />
            </DialogContent>
          </Dialog>
        </div>

        <Table.StickyTable
          headerItems={[['Name', 'Action']]}
          rowItems={checkpointRows}
        />
      </div>
    </div>
  );
}
