import { AttendanceTimeSeriesChart } from '@/components/charts/AttendanceTimeSeriesChart';
import { GuardActivityOverview } from '@/components/charts/GuardActivityOverview';
import { InventoryStatusChart } from '@/components/charts/InventoryStatusChart';
import { LeaveStatusChart } from '@/components/charts/LeaveStatusChart';
import { AnalyticsFilter } from '@/components/charts/AnalyticsFilter';
import { createFileRoute } from '@tanstack/react-router';
import { useState, useMemo } from 'react';
import { DateRange } from 'react-day-picker';
import {
  UserRoles,
  useGuardAttendanceTrendsQuery,
  useGuardActivityStatsQuery,
  useLeaveStatsQuery,
  useInventoryStatsQuery,
  useLocationsQuery,
} from '@/generated/graphql';

export const Route = createFileRoute('/')({
  component: RouteComponent
});

function RouteComponent() {
  const [filters, setFilters] = useState<{
    dateRange?: DateRange;
    userRole?: UserRoles;
    locationId?: string;
  }>({});

  // Convert filters to GraphQL input format
  const filterInput = useMemo(() => ({
    dateRange: filters.dateRange ? {
      startDate: filters.dateRange.from!,
      endDate: filters.dateRange.to!
    } : undefined,
    userRole: filters.userRole,
    locationId: filters.locationId
  }), [filters]);

  // Fetch data using GraphQL queries
  const { data: attendanceData, isLoading: attendanceLoading } = useGuardAttendanceTrendsQuery(
    { filter: filterInput },
    { enabled: !!filterInput.dateRange }
  );

  const { data: activityData, isLoading: activityLoading } = useGuardActivityStatsQuery({
    filter: filterInput
  });

  const { data: leaveData, isLoading: leaveLoading } = useLeaveStatsQuery({
    filter: filterInput
  });

  const { data: inventoryData, isLoading: inventoryLoading } = useInventoryStatsQuery();

  const { data: locations, isLoading: locationLoading } = useLocationsQuery();

  const handleFilterChange = ({
    dateRange,
    userRole,
    locationId
  }: {
    dateRange?: DateRange;
    userRole?: UserRoles;
    locationId?: string;
  }) => {
    setFilters({ dateRange, userRole, locationId });
  };

  const isLoading = attendanceLoading || activityLoading || leaveLoading || inventoryLoading || locationLoading;

  return (
    <div className="p-4">
      <AnalyticsFilter
        onFilterChange={handleFilterChange}
        locations={locations?.locations}
      />
      {isLoading ? (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2">
          <div className="rounded-lg border p-4">
            <h3 className="font-semibold">Attendance Trends</h3>
            <AttendanceTimeSeriesChart 
              data={attendanceData?.guardAttendanceTrends || []} 
            />
          </div>
          <div className="rounded-lg border p-4">
            <h3 className="font-semibold">Guard Activity</h3>
            <GuardActivityOverview 
              data={activityData?.guardActivityStats!} 
            />
          </div>
          <div className="rounded-lg border p-4">
            <h3 className="font-semibold">Leave Status</h3>
            <LeaveStatusChart 
              data={leaveData?.leaveStats!} 
            />
          </div>
          <div className="rounded-lg border p-4">
            <h3 className="font-semibold">Inventory Status</h3>
            <InventoryStatusChart 
              data={inventoryData?.inventoryStats || []} 
            />
          </div>
        </div>
      )}
    </div>
  );
}
