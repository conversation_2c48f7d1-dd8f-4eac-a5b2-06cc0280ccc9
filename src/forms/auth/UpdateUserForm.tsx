import { FormComponentProps } from '@/@types';
import <PERSON>Field from '@/components/forms/FormField';
import FormPhoneInput from '@/components/forms/FormPhoneInput';
import FormSelect from '@/components/forms/FormSelect';
import { Button } from '@/components/ui/button';
import { SelectGroup, SelectItem, SelectLabel } from '@/components/ui/select';
import {
  UserRoles,
  UserStatus,
  useUpdateUserMutation,
  useUserQuery,
  useUsersQuery
} from '@/generated/graphql';
import { data } from '@/lib/data';
import { enumToOptions } from '@/lib/utils';
import { queryClient } from '@/main';
import { zodResolver } from '@hookform/resolvers/zod';

import { FormProvider, useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

/**Form Schema */
const schema = z.object({
  userId: z.string(),
  phone: z.string().nonempty('Phone number must be at least 10 digits'),
  fullname: z.string().nonempty('Please enter full name'),
  role: z.nativeEnum(UserRoles, {
    message: 'Please select a role'
  }),
  userStatus: z.nativeEnum(UserStatus, {
    message: 'Please select a status'
  })
});

/**Form Types */
type UpdateUserForm = z.infer<typeof schema>;

/**Form Methods */
export const useUpdateUserFormMethods = (onSuccess?: () => void) => {
  const methods = useForm<UpdateUserForm>({
    resolver: zodResolver(schema),
    defaultValues: {
      phone: '',
      fullname: '',
      role: UserRoles.LocalGuard,
      userStatus: UserStatus.Active
    }
  });

  const { mutateAsync: updateUser } = useUpdateUserMutation();

  const onSubmit = async ({
    role,
    userStatus,
    userId,
    ...data
  }: UpdateUserForm) => {
    toast.promise(
      updateUser({
        input: {
          id: userId,
          role,
          userStatus,
          ...data
        }
      }),
      {
        loading: 'Updating user...',
        success: () => {
          queryClient.invalidateQueries({ queryKey: useUsersQuery.getKey() });
          return 'User updated successfully';
        },
        error: 'Failed to update user'
      }
    );
  };
  return { onSubmit, methods };
};

/**Form Component */
export default function UpdateUserForm({
  methods,
  onSubmit
}: FormComponentProps<UpdateUserForm>) {
  return (
    <FormProvider {...methods}>
      <form className="space-y-4" onSubmit={methods.handleSubmit(onSubmit)}>
        <div className="grid grid-cols-2 gap-4">
          <FormField
            name="fullname"
            placeholder="Enter full name"
            label="Full Name"
          />
          <FormPhoneInput
            name="phone"
            placeholder="Enter phone number"
            label="Phone"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <FormSelect name="role" label="Role" placeholder="Select a role">
            <SelectGroup>
              <SelectLabel>Select Role</SelectLabel>
              {enumToOptions(UserRoles).map(({ value, label }) => (
                <SelectItem value={value} key={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectGroup>
          </FormSelect>
          <FormSelect
            name="userStatus"
            label="Status"
            placeholder="Select user status"
          >
            <SelectGroup>
              <SelectLabel>Select status</SelectLabel>
              {enumToOptions(UserStatus).map(({ value, label }) => (
                <SelectItem value={value} key={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectGroup>
          </FormSelect>
        </div>

        <Button className="w-full">Update User</Button>
      </form>
    </FormProvider>
  );
}
