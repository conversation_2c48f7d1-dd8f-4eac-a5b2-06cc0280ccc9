import { Form<PERSON>rovider, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import FormField from '@/components/forms/FormField';
import FormTextArea from '@/components/forms/FormTextArea';
import FormPhoneInput from '@/components/forms/FormPhoneInput';
import { useUpdateLocationMutation } from '@/generated/graphql';
import { toast } from 'sonner';

const schema = z.object({
  name: z.string().nonempty('Name is required'),
  description: z.string().optional(),
  address: z.string().optional(),
  emergencyContact: z.string().optional()
});

type UpdateLocationForm = z.infer<typeof schema>;

interface UpdateLocationFormProps {
  location: {
    id: string;
    name?: string;
    description?: string;
    address?: string;
    emergencyContact?: string;
  }
}

export default function UpdateLocationForm({ location }: UpdateLocationFormProps) {
  const methods = useForm<UpdateLocationForm>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: location?.name || '',
      description: location?.description || '',
      address: location?.address || '',
      emergencyContact: location?.emergencyContact || ''
    }
  });

  const { mutateAsync: updateLocation } = useUpdateLocationMutation();

  const onSubmit = async (data: UpdateLocationForm) => {
    toast.promise(
      updateLocation({ 
        id: location.id,
        updateLocationInput: data 
      }), 
      {
        loading: 'Updating location...',
        success: 'Location updated successfully',
        error: 'Failed to update location'
      }
    );
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
        <FormField name="name" label="Name" placeholder="Enter location name" />
        <FormTextArea
          name="description"
          label="Description"
          placeholder="Enter location description"
        />
        <FormTextArea
          name="address"
          label="Address"
          placeholder="Enter location address"
          className="min-h-24"
        />
        <FormPhoneInput
          name="emergencyContact"
          label="Emergency Contact"
          placeholder="Emergency contact"
        />

        <Button type="submit">Update Location</Button>
      </form>
    </FormProvider>
  );
}
