import { CodegenConfig } from '@graphql-codegen/cli';
import { VITE_GRAPHQL_URL } from './src/env';

const config: CodegenConfig = {
  schema:
    'https://5ev2sfhl92.execute-api.ap-southeast-1.amazonaws.com/production/graphql',
  // schema: VITE_GRAPHQL_URL,
  documents: ['./src/graphql/**/*.gql'],
  ignoreNoDocuments: true, // for better experience with the watcher
  generates: {
    './src/generated/graphql.ts': {
      plugins: [
        'typescript',
        'typescript-operations',
        'typescript-react-query'
      ],
      config: {
        isReactHook: false,
        fetcher: '@/client#fetchData',
        errorType: 'Error',
        exposeDocument: true,
        exposeQueryKeys: true,
        legacyMode: false,
        reactQueryVersion: 5,
        scalars: {
          DateTime: 'Date',
          JSON: 'any'
        }
      }
    }
  }
};

export default config;
